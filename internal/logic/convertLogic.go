package logic

import (
	"context"

	"shorturl/internal/svc"
	"shorturl/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConvertLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConvertLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConvertLogic {
	return &ConvertLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConvertLogic) Convert(req *types.ConvertRequest) (resp *types.ConvertResponse, err error) {
	// todo: add your logic here and delete this line
	// 1.参数校验
	// 1.1.参数规则校验 在handler/converandler.go里面已经实现
	// 1.2 输入的长链接必须是一个能够请求同的网址
	// 1.3 判断之前是否转链过（数据库中是否已存在长链接）
	// 1.4 输入的不能是一个短链接（避免循环转链）
	return
}
