package logic

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"shorturl/internal/svc"
	"shorturl/internal/types"
	"shorturl/pkg/connect"
	"shorturl/pkg/md5"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ConvertLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConvertLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConvertLogic {
	return &ConvertLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Convert 转链业务逻辑：输入一个长链接->转为短链接
func (l *ConvertLogic) Convert(req *types.ConvertRequest) (resp *types.ConvertResponse, err error) {
	// todo: add your logic here and delete this line
	// 1. 参数校验
	// 1.1. 参数规则校验 在handler/converandler.go里面已经实现
	// 1.2. 输入的长链接必须是一个能够请求通的网址
	if ok := connect.Get(l.ctx, req.LongUrl); !ok {
		return nil, errors.New("无效的链接")
	}
	// 1.3. 判断之前是否转链过（数据库中是否已存在长链接）
	// 将LongURL转为MD5字符串，然后去数据库中查看
	longUrlMD5 := md5.NewMD5String(req.LongUrl)
	u, err := l.svcCtx.ShortUrlMap.FindOneByMd5(l.ctx, sql.NullString{String: longUrlMD5, Valid: true})
	if err != sqlx.ErrNotFound {
		if err!=nil{
			return nil,errors.New(fmt.Sprintf(""))
		}
	}
	// 1.4. 输入的不能是一个短链接（避免循环转链）
	// 2. 取号
	// 3. 号码转短链
	// 4. 存储长链接映射关系
	// 5. 返回短链接
	return
}
